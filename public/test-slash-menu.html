<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slash Menu Position Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            height: 400px;
            border: 1px solid #ccc;
            position: relative;
            overflow: auto;
            padding: 10px;
        }
        
        .editor {
            min-height: 800px;
            line-height: 1.5;
            font-size: 14px;
        }
        
        .line {
            min-height: 21px;
            padding: 2px 0;
            position: relative;
        }
        
        .menu {
            position: absolute;
            width: 300px;
            max-height: 300px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .menu-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .menu-item:hover {
            background: #f5f5f5;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .test-controls {
            margin-bottom: 20px;
        }
        
        button {
            margin-right: 10px;
            padding: 8px 16px;
            cursor: pointer;
        }
        
        .info {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Slash Menu Position Test</h1>
    
    <div class="test-controls">
        <button onclick="showMenuBelow()">Show Menu Below (Normal)</button>
        <button onclick="showMenuAbove()">Show Menu Above (Fixed)</button>
        <button onclick="hideMenu()">Hide Menu</button>
    </div>
    
    <div class="container" id="container">
        <div class="editor">
            <div class="line">Line 1: This is a test line</div>
            <div class="line">Line 2: Another test line</div>
            <div class="line">Line 3: More content here</div>
            <div class="line">Line 4: Keep adding lines</div>
            <div class="line">Line 5: To create scrollable content</div>
            <div class="line">Line 6: So we can test the menu positioning</div>
            <div class="line">Line 7: When there's not enough space below</div>
            <div class="line">Line 8: The menu should appear above</div>
            <div class="line">Line 9: Without covering the current line</div>
            <div class="line">Line 10: This is the target line /</div>
            <div class="line">Line 11: More content below</div>
            <div class="line">Line 12: To test positioning</div>
            <div class="line">Line 13: Additional content</div>
            <div class="line">Line 14: More lines</div>
            <div class="line">Line 15: Even more content</div>
            <div class="line">Line 16: Final test line</div>
        </div>
    </div>
    
    <div class="menu" id="menu" style="display: none;">
        <div class="menu-item">📝 Text</div>
        <div class="menu-item">🎯 AI Continue</div>
        <div class="menu-item">✨ AI Assistant</div>
        <div class="menu-item">📊 Table</div>
        <div class="menu-item">💬 Blockquote</div>
        <div class="menu-item">🖼️ Image</div>
        <div class="menu-item">🔗 Link</div>
        <div class="menu-item">➡️ Horizontal Slide</div>
        <div class="menu-item">⬇️ Vertical Slide</div>
        <div class="menu-item">📝 Notes</div>
    </div>
    
    <div class="info">
        <h3>Test Instructions:</h3>
        <p>1. Scroll down so that "Line 10" is near the bottom of the container</p>
        <p>2. Click "Show Menu Above (Fixed)" to test the corrected positioning</p>
        <p>3. The menu should appear above Line 10 without covering it</p>
        <p>4. Compare with "Show Menu Below (Normal)" to see the difference</p>
    </div>

    <script>
        const menu = document.getElementById('menu');
        const container = document.getElementById('container');
        const targetLine = document.querySelector('.line:nth-child(10)');
        
        function showMenuBelow() {
            const rect = targetLine.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            menu.style.display = 'block';
            menu.style.left = (rect.left - containerRect.left + container.scrollLeft) + 'px';
            menu.style.top = (rect.bottom - containerRect.top + container.scrollTop) + 'px';
            menu.style.bottom = 'auto';
        }
        
        function showMenuAbove() {
            const rect = targetLine.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            // Fixed calculation: menu appears above the line without covering it
            const bottomValue = containerRect.height - (rect.top - containerRect.top);
            
            menu.style.display = 'block';
            menu.style.left = (rect.left - containerRect.left + container.scrollLeft) + 'px';
            menu.style.top = 'auto';
            menu.style.bottom = bottomValue + 'px';
        }
        
        function hideMenu() {
            menu.style.display = 'none';
        }
        
        // Auto-scroll to show the target line near bottom
        setTimeout(() => {
            targetLine.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }, 500);
    </script>
</body>
</html>
