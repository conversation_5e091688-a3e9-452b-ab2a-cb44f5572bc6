# Test Slides

This is a test slide presentation to verify the slash command menu positioning fix.

---

## Slide 2

Some content here to create multiple slides.

---

## Slide 3

More content to test the editor.

---

## Slide 4

Additional content for testing.

---

## Slide 5

Even more content.

---

## Slide 6

Testing the slash menu positioning.

---

## Slide 7

When you type '/' at the bottom of the editor, the menu should appear above without covering the current line.

---

## Slide 8

This is where you can test the fix.

---

## Slide 9

More test content.

---

## Slide 10

Final test slide.

---

## Slide 11

Last slide for testing the slash menu positioning when there's not enough space below.

Type '/' here to test: 
