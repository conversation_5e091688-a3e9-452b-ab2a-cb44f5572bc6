/* @flow */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// import SpinnerAndToast, { showMessage } from './SpinnerAndToast';
import { get_server_host } from '../../utils/serverAPIUtil';
import LoadingScreen from '../LoadingScreen';
import { SlideSettings } from '@styled-icons/fluentui-system-regular/SlideSettings';
import { Selector } from '../common/Selector';
import { slides_themes } from '../../constants/constants';
import { getSlidesConfig, upsertDoc } from '../../actions/ticketAction';
import { sendMessageToPreview } from '../../utils/SlidesUtil';
import { Refresh } from '@styled-icons/material';

const SlidesPreview = ({ hid, viewerReady, resetMarkdown, onSlideChange, refreshId, currentSlide }) => {
    const dispatch = useDispatch();

    const [server_host, setServer_host] = useState();
    const [loading, setLoading] = useState(true);
    const [config, setConfig] = useState();

    const container = useRef(null);

    const onWebMessage = (event) => {
        if ((event.origin + '/') !== server_host) {
            return;
        }

        if (process.env.NODE_ENV !== 'production') {
            console.log('get message from web..............', event.data)
        }

        let message = event.data;

        if (typeof message === 'string') {
            try {
                message = JSON.parse(message);
            } catch (err) {
                console.log(err);
            }
        }

        switch (message.type) {
            case "revealinited":
                setLoading(false);
                if (viewerReady) {
                    viewerReady();
                }
                break;
            case "resetMarkdown":
                if (resetMarkdown) {
                    resetMarkdown();
                }
                break;
            case "slidechanged":
                if (onSlideChange && message.data?.indexh !== undefined && message.data?.indexv !== undefined) {
                    onSlideChange(message.data.indexh, message.data.indexv);
                }
                break;
        }
    }

    useEffect(() => {
        get_server_host().then((value) => setServer_host(value));
    }, []);

    useEffect(() => {
        if(!hid) return;

        dispatch(getSlidesConfig({hid}, (config) => {
            setConfig(config)
        }, 'slides_preview'))
    }, [hid])

    useEffect(() => {
        if (server_host) {
            window.addEventListener('message', onWebMessage);
        }

        return () => {
            window.removeEventListener("message", onWebMessage);
        }
    }, [server_host, onWebMessage]);

    const themes = useMemo(() => slides_themes.map(theme => ({
        label: theme.name,
        value: theme.name
    })), [])

    const onThemeChange = useCallback((theme) => {
        const updatedConfig = {
            ...(config || {}),
            theme
        }
        dispatch(upsertDoc({
            data: {
                doc: { meta: updatedConfig, hid }
            }
        }))

        setConfig(updatedConfig)
    }, [hid])

    const [loaded, setLoaded] = useState();
    const [refresher, setRefresher] = useState(1)

    useEffect(() => {
        if(!loaded || !config?.theme) return;

        sendMessageToPreview(server_host, 'theme_changed', [config.theme])
    }, [loaded, config?.theme])

    useEffect(() => {
        if(!loaded || !server_host) return;

        // 设置幻灯片切换监听器
        sendMessageToPreview(server_host, 'setupSlideChangeListener', [])
    }, [loaded, server_host])

    // console.log('presentation src..............', `${server_host}view.html?mode=preview&showNotes=true&hid=${hid}&rf=${refresher + refreshId}`)

    return (
        <div
            style={{ position: 'relative', width: '100%', height: '100%', overflow: 'hidden' }}
            ref={container}
        >
            {loading && <LoadingScreen style={{ height: '100px' }} />}
            <iframe
                id="slides-frame"
                style={{ width: '100%', height: '100%' }}
                frameBorder="0"
                src={`${server_host}view.html?mode=preview&showNotes=true&hid=${hid}&rf=${refresher + refreshId}/#/${currentSlide.h}/${currentSlide.v}`}
                allow="camera *;microphone *"
                onLoad={() => setLoaded(true)}
            />
            <div style={{
                position: 'absolute',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                top: 20,
                right: 20,
                gap: 10
            }}>
                <div style={{ cursor: 'pointer' }} onClick={() => setRefresher(Math.floor(Math.random()*100000))}>
                    <Refresh color='dodgerblue' size={32} />
                </div>
                <Selector
                    triggerElement={<SlideSettings color='dodgerblue' size={32} />}
                    value={config?.theme}
                    options={themes}
                    onChange={onThemeChange}
                    inputStyle={{
                        backgroundColor: 'transparent',
                        border: 'none'
                    }}
                    itemStyle={{
                        padding: '10px',
                        fontSize: 16,
                    }}
                />
            </div>
        </div>
    )
}

export default SlidesPreview;
